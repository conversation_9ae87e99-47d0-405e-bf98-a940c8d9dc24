---
output:
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float", "tikz", "xcolor"]
  word_document: default
  html_document: default
icfes:
  competencia:
    - argumentacion
  nivel_dificultad: 3
  contenido:
    categoria: estadistica
    tipo: generico
  contexto: comunitario
  eje_axial: eje4
  componente: aleatorio
---

```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{xcolor}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)
library(knitr)
library(stringr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)

# Semilla aleatoria para diversidad de versiones
set.seed(sample(1:100000, 1))
```

```{r data_generation, echo=FALSE, results="hide"}
# Función principal de generación de datos para competencia ARGUMENTACIÓN
generar_datos <- function() {
  # Contextos aleatorios ampliados para mayor diversidad
  contextos <- list(
    list(producto = "empaques de Tetra Pak", empresa = "industria alimentaria", material_principal = "cartón", uso = "conservación de alimentos"),
    list(producto = "envases multicapa", empresa = "industria de bebidas", material_principal = "polímero", uso = "protección de líquidos"),
    list(producto = "contenedores asépticos", empresa = "sector lácteo", material_principal = "cartón", uso = "preservación de lácteos"),
    list(producto = "empaques flexibles", empresa = "industria farmacéutica", material_principal = "aluminio", uso = "protección de medicamentos"),
    list(producto = "envases compuestos", empresa = "sector alimentario", material_principal = "cartón", uso = "almacenamiento de productos"),
    list(producto = "recipientes multicapa", empresa = "industria de jugos", material_principal = "polietileno", uso = "conservación de bebidas"),
    list(producto = "empaques sostenibles", empresa = "sector ecológico", material_principal = "cartón", uso = "protección ambiental"),
    list(producto = "contenedores híbridos", empresa = "industria química", material_principal = "polímero", uso = "almacenamiento seguro")
  )

  contexto_sel <- sample(contextos, 1)[[1]]

  # Aleatorizar porcentajes de materiales (manteniendo suma = 100%)
  # Material principal (60-80%), Material secundario (15-30%), Material terciario (5-15%)
  porcentaje_principal <- sample(60:80, 1)
  porcentaje_restante <- 100 - porcentaje_principal
  porcentaje_secundario <- sample(15:min(30, porcentaje_restante-5), 1)
  porcentaje_terciario <- porcentaje_restante - porcentaje_secundario

  # Asignar materiales según contexto
  materiales <- c("Cartón", "Polietileno", "Aluminio")
  porcentajes <- c(porcentaje_principal, porcentaje_secundario, porcentaje_terciario)
  
  # Aleatorizar orden de materiales
  orden_materiales <- sample(1:3)
  materiales_ordenados <- materiales[orden_materiales]
  porcentajes_ordenados <- porcentajes[orden_materiales]

  # Aleatorizar tipo de empaque y escala
  tipos_empaque <- c("1 litro", "500 ml", "250 ml", "2 litros", "1.5 litros")
  empaque_original <- sample(tipos_empaque, 1)
  
  escalas_reduccion <- c("la mitad", "la tercera parte", "la cuarta parte", "dos tercios")
  escala_sel <- sample(escalas_reduccion, 1)
  
  # Calcular factor de escala lineal y volumétrico
  factores_lineales <- list(
    "la mitad" = 0.5,
    "la tercera parte" = 1/3,
    "la cuarta parte" = 0.25,
    "dos tercios" = 2/3
  )
  
  factor_lineal <- factores_lineales[[escala_sel]]
  factor_volumen <- factor_lineal^3

  return(list(
    contexto = contexto_sel,
    materiales = materiales_ordenados,
    porcentajes = porcentajes_ordenados,
    empaque_original = empaque_original,
    escala_reduccion = escala_sel,
    factor_lineal = factor_lineal,
    factor_volumen = factor_volumen
  ))
}

# Generar datos del ejercicio
datos <- generar_datos()

# Extraer variables individuales para facilitar uso
contexto <- datos$contexto
materiales <- datos$materiales
porcentajes <- datos$porcentajes
empaque_original <- datos$empaque_original
escala_reduccion <- datos$escala_reduccion
factor_lineal <- datos$factor_lineal
factor_volumen <- datos$factor_volumen
```

```{r version_diversity_test, echo=FALSE, results="hide"}
# Pruebas de diversidad y coherencia - Solo en modo desarrollo
# Estas pruebas se ejecutan solo cuando se renderiza directamente el .Rmd
# No durante la generación de exámenes con exams2html()

if(!exists(".exams_generation_mode") || !.exams_generation_mode) {

  # Prueba de diversidad de versiones para competencia ARGUMENTACIÓN
  test_that("Prueba de diversidad de versiones", {
    versiones <- list()
    for(i in 1:1000) {
      datos_test <- generar_datos()
      versiones[[i]] <- digest::digest(datos_test)
    }

    n_versiones_unicas <- length(unique(versiones))
    expect_true(n_versiones_unicas >= 300,
                info = paste("Solo se generaron", n_versiones_unicas,
                            "versiones únicas. Se requieren al menos 300."))
  })

  test_that("Prueba de coherencia matemática", {
    for(i in 1:20) {
      datos_test <- generar_datos()

      # Verificar que los porcentajes sumen 100%
      expect_equal(sum(datos_test$porcentajes), 100,
                  info = "Los porcentajes deben sumar exactamente 100%")

      # Verificar que el factor volumétrico sea correcto
      expect_equal(datos_test$factor_volumen, datos_test$factor_lineal^3,
                  info = "El factor volumétrico debe ser el cubo del factor lineal")

      # Verificar rangos válidos de porcentajes
      expect_true(all(datos_test$porcentajes >= 5 & datos_test$porcentajes <= 80),
                 info = "Los porcentajes deben estar en rangos realistas")
    }
  })

} else {
  # En modo generación de exámenes, solo verificar que la función funciona
  datos_verificacion <- generar_datos()
  stopifnot(sum(datos_verificacion$porcentajes) == 100)
  stopifnot(abs(datos_verificacion$factor_volumen - datos_verificacion$factor_lineal^3) < 1e-10)
}
```

```{r generar_grafico_python, echo=FALSE, results="hide"}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Preparar datos para Python
materiales_str <- paste0("['", paste(materiales, collapse = "', '"), "']")
porcentajes_str <- paste0("[", paste(porcentajes, collapse = ", "), "]")

# Código Python para el gráfico circular
codigo_python_grafico <- paste0("
import matplotlib.pyplot as plt
import numpy as np
import random
import matplotlib
matplotlib.rcParams['font.size'] = 9

# Datos del gráfico
materiales = ", materiales_str, "
porcentajes = ", porcentajes_str, "

# Configuración de colores aleatorios
colores_disponibles = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
colores_seleccionados = random.sample(colores_disponibles, len(materiales))

# Crear gráfico circular
plt.figure(figsize=(8, 8))
wedges, texts, autotexts = plt.pie(porcentajes, labels=materiales, autopct='%1.0f%%',
                                   colors=colores_seleccionados, startangle=90,
                                   textprops={'fontsize': 12, 'weight': 'bold'})

# Configurar título
plt.title('Composición de Materiales del Empaque', fontsize=16, weight='bold', pad=20)

# Mejorar apariencia
for autotext in autotexts:
    autotext.set_color('white')
    autotext.set_weight('bold')

plt.tight_layout()
plt.savefig('grafico_composicion.png', dpi=150, bbox_inches='tight')
plt.close()
")

# Ejecutar código Python
py_run_string(codigo_python_grafico)
```

```{r generar_distractores, echo=FALSE, results="hide"}
# SISTEMA AVANZADO DE DISTRACTORES para competencia ARGUMENTACIÓN
# DECISIÓN ALEATORIA: ¿Permitir valores duplicados con justificaciones diferentes?
# 30% de probabilidad de generar opciones con mismo concepto pero diferentes explicaciones
permitir_conceptos_duplicados <- sample(c(TRUE, FALSE), 1, prob = c(0.3, 0.7))

# Generar afirmaciones incorrectas (distractores)
afirmaciones_incorrectas <- c()

# DISTRACTOR 1: Confundir escalamiento lineal con volumétrico
afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
  paste0("Los porcentajes se duplicarían al haber menos espacio vacío dentro del empaque"))

# DISTRACTOR 2: Confundir con conservación de masa
afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
  paste0("Los porcentajes se conservarían sin importar el tamaño del empaque"))

# DISTRACTOR 3: Error sobre proporcionalidad
if(escala_reduccion == "la mitad") {
  factor_incorrecto <- 1/8
  afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
    paste0("Los porcentajes se reducirían a la octava parte porque todas las caras se reducen a la mitad"))
} else {
  afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
    paste0("Los porcentajes dependerían de las dimensiones que tuviera el empaque de ", escala_reduccion))
}

# DISTRACTOR 4: Confusión sobre densidad de materiales
afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
  paste0("Los porcentajes cambiarían porque la densidad de los materiales es diferente en empaques pequeños"))

# DISTRACTOR 5: Error sobre superficie vs volumen
afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
  paste0("Los porcentajes se mantendrían porque la superficie del empaque no cambia proporcionalmente"))

# DISTRACTOR 6: Confusión sobre grosor de materiales
afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
  paste0("Los porcentajes cambiarían porque el grosor de las capas no se puede reducir proporcionalmente"))

# JUSTIFICACIONES ALTERNATIVAS para el concepto correcto (pero con razonamiento incorrecto)
justificaciones_incorrectas_concepto_correcto <- c()

if(permitir_conceptos_duplicados) {
  justificaciones_incorrectas_concepto_correcto <- c(
    paste0("Los porcentajes se conservarían porque la masa total del empaque se mantiene constante"),
    paste0("Los porcentajes se conservarían porque la densidad de los materiales no cambia"),
    paste0("Los porcentajes se conservarían porque la composición química de los materiales es la misma")
  )

  # Agregar justificaciones incorrectas para el concepto correcto
  afirmaciones_incorrectas <- c(afirmaciones_incorrectas, justificaciones_incorrectas_concepto_correcto)
}

# Eliminar duplicados
afirmaciones_incorrectas <- unique(afirmaciones_incorrectas)

# Seleccionar 3 distractores
if(length(afirmaciones_incorrectas) >= 3) {
  distractores_seleccionados <- sample(afirmaciones_incorrectas, 3)
} else {
  # Fallback si no hay suficientes distractores
  distractores_seleccionados <- c(
    "Los porcentajes se duplicarían al haber menos espacio vacío dentro del empaque",
    "Los porcentajes dependerían de las dimensiones que tuviera el empaque reducido",
    "Los porcentajes cambiarían porque la densidad de los materiales es diferente en empaques pequeños"
  )
}

# Afirmación correcta
afirmacion_correcta <- "Los porcentajes se conservarían sin importar el tamaño del empaque"

# Crear las 4 opciones finales
todas_opciones <- c(afirmacion_correcta, distractores_seleccionados)
opciones_mezcladas <- sample(todas_opciones)

# Identificar posición correcta
pos_correcta <- which(opciones_mezcladas == afirmacion_correcta)
```

Question
========

Los `r contexto$producto` son elaborados con `r materiales[1]`, `r materiales[2]` y `r materiales[3]`, distribuidos en 6 capas, lo cual evita el contacto del alimento con el medio externo. La gráfica muestra la distribución porcentual aproximada de los materiales de un `r contexto$producto`:

```{r mostrar_grafico, echo=FALSE, results='asis', fig.align='center'}
# Mostrar el gráfico generado con Python
knitr::include_graphics("grafico_composicion.png")
```

Las 6 capas del empaque se distribuyen así:

**Primera capa.** `r materiales[2]`: protege los alimentos de la humedad atmosférica externa.

**Segunda capa.** `r materiales[1]`: brinda resistencia, forma y estabilidad.

**Tercera capa.** `r materiales[2]`: ofrece adherencia fijando las capas de papel y aluminio.

**Cuarta capa.** `r materiales[3]`: evita la entrada de oxígeno y luz, y la pérdida de aromas.

**Quinta capa.** `r materiales[2]`: evita que el alimento esté en contacto con el `r materiales[3]`.

**Sexta capa.** `r materiales[2]`: garantiza por completo la protección del alimento.

Una persona afirma que los porcentajes de los materiales en el empaque son válidos para un empaque de `r empaque_original`, pero que si se construye con la misma técnica un empaque de `r escala_reduccion`, reduciendo las dimensiones a `r escala_reduccion`, entonces los porcentajes también se reducen a `r escala_reduccion`.

**Esta afirmación es falsa porque:**

Answerlist
----------
- `r opciones_mezcladas[1]`
- `r opciones_mezcladas[2]`
- `r opciones_mezcladas[3]`
- `r opciones_mezcladas[4]`

Solution
========

Para resolver este problema de **argumentación matemática**, debemos analizar la afirmación falsa y determinar por qué es incorrecta según los principios de escalamiento y proporcionalidad.

**Análisis de la afirmación falsa:**

La persona afirma que al reducir las dimensiones del empaque a `r escala_reduccion`, los porcentajes de materiales también se reducen a `r escala_reduccion`. Esta afirmación es **matemáticamente incorrecta**.

**¿Por qué es falsa la afirmación?**

```{r analisis_escalamiento, echo=FALSE, results='asis'}
cat("**Principio fundamental:** Los porcentajes representan proporciones relativas, no cantidades absolutas.\n\n")

cat("**Escalamiento de dimensiones:**\n")
cat("- Factor de reducción lineal: ", factor_lineal, "\n")
cat("- Factor de reducción volumétrica: ", factor_lineal, "³ = ", round(factor_volumen, 4), "\n\n")

cat("**Composición del empaque original:**\n")
for(i in 1:length(materiales)) {
  cat("- ", materiales[i], ": ", porcentajes[i], "%\n")
}
cat("\n")

cat("**Composición del empaque reducido:**\n")
cat("Los porcentajes se mantienen **exactamente iguales** porque:\n\n")
cat("1. **Proporcionalidad:** Todos los materiales se reducen en la misma proporción\n")
cat("2. **Conservación de ratios:** La relación entre materiales no cambia\n")
cat("3. **Escalamiento uniforme:** Las 6 capas mantienen sus proporciones relativas\n\n")
```

**Demostración matemática:**

Si el empaque original tiene volumen V y el reducido tiene volumen `r round(factor_volumen, 4)`V:

- `r materiales[1]` original: `r porcentajes[1]`% de V
- `r materiales[1]` reducido: `r porcentajes[1]`% de `r round(factor_volumen, 4)`V = `r round(porcentajes[1] * factor_volumen, 2)`% de V

Pero como porcentaje del nuevo volumen total: `r round(porcentajes[1] * factor_volumen, 2)`% de V ÷ `r round(factor_volumen, 4)`V = **`r porcentajes[1]`%**

**Conclusión:**

La afirmación correcta es: **"`r afirmacion_correcta`"**

Los porcentajes son **invariantes bajo escalamiento uniforme** porque representan proporciones relativas, no cantidades absolutas.

**¿Por qué las otras afirmaciones son incorrectas?**

- Confunden escalamiento lineal con cambios en composición
- No comprenden que los porcentajes son ratios, no cantidades
- Aplican incorrectamente conceptos de densidad o superficie
- No reconocen la invariancia de proporciones bajo escalamiento uniforme

Answerlist
----------
```{r respuestas, echo=FALSE, results='asis'}
for(i in 1:4) {
  if(i == pos_correcta[1]) {
    cat("- Verdadero\n")
  } else {
    cat("- Falso\n")
  }
}
```

Meta-information
================
exname: empaques_tetra_pak_argumentacion_escalamiento
extype: schoice
exsolution: `r paste(as.integer(1:4 == pos_correcta[1]), collapse="")`
exshuffle: TRUE
exsection: Argumentación en Estadística y Proporcionalidad
